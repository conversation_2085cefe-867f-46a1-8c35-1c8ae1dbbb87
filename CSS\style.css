* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

body {
  background-color: #f0f0f0;
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
}


::-webkit-scrollbar {
  width: 12px; 
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: #fc5d35;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}


::-webkit-scrollbar-thumb:hover {
  background: #b03820; 
}



/* Container */
.container {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 4rem;
    padding-right: 4rem;
  }
}

@media (min-width: 1280px) {
  .container {
    padding-left: 5rem;
    padding-right: 5rem;
  }
}

@media (min-width: 1536px) {
  .container {
    padding-left: 6rem;
    padding-right: 6rem;
  }
}

/* Navbar */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
  
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.logo a {
  text-decoration: none;
  color: #333;
}

.menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navbar ul {
  display: flex;
  list-style: none;
  gap: 3rem;
}

.navbar ul li a {
  text-decoration: none;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

/* Hover & Active */
.navbar ul li a:hover,
.navbar ul li a.active {
  color: #fff;
  background-color: #fc5d35;
  font-weight: bold;
}

.navbar .icon a img {
  width: 2rem;
  height: 2rem;
}

/* Hero Section */
.hero {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  height: calc(100vh - 96px);
  padding: 2rem 0;
  gap: 2rem;
}

.hero .content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 1.5rem;
}

.hero h1 {
  font-size: 4rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.5rem;
}
.hero h1 span {
  color: #fc5d35;
  font-size: 5rem;
}

.hero p {
  font-size: 1.25rem;
  color: #666;
  margin-bottom: 1rem;
  max-width: 600px;
}

.hero .btn {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn button {
  padding: 1rem 2rem;
  background-color: #fc5d35;
  color: #fff;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}

.btn button:hover {
  background-color: #e64a29;
  transform: scale(1.05);
}

.hero .contact {
  background-color: #333;
}
.hero .contact:hover {
  background-color: #222;
}

.hero .image {
  flex: 1 1 500px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero .image img {
  max-width: 100%;
  height: auto;
}
/* Services Section */
.services {
  padding: 3rem 0;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.services .content {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.services .content h2 {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 1rem;
}

.services .content p {
  font-size: 1.125rem;
  color: #666;
  line-height: 1.8;
}

.services .cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  width: 100%;
}

.services .card {
  background-color: #fff;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  cursor: pointer;
}

.services .card:hover {
  transform: translateY(-15px);
  box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
}

/* Hover Effects */
.services .card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 6px;
  background-color: #fc5d35;
  border-radius: 1rem;
  transition: width 0.3s ease;
  z-index: 2;
}

.services .card:hover::before {
  width: 100%;
}

.services .card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(252, 93, 53, 0.07);
  border-radius: 1rem;
  z-index: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.services .card:hover::after {
  opacity: 1;
}

.services .card .icon img {
  width: 64px;
  height: 64px;
}

.services .card .content h3 {
  font-size: 1.5rem;
  color: #fc5d35;
  margin-bottom: 0.5rem;
}

.services .card .content p {
  font-size: 1rem;
  color: #555;
  line-height: 1.6;
  text-align: center;
}

.services .btn {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

/* Products Section */
.products {
  padding: 2rem 0;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.products .products-header {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.products .products-header h2 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.products .products-header p {
  font-size: 1.125rem;
  color: #666;
  line-height: 1.8;
}

.products .cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
}

.products .card {
  background-color: #fff;
  border-radius: 1rem;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row;
  gap: 2rem;
  flex-wrap: wrap;
  max-width: 1140px;
  margin: auto;
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.products .card:hover {
  transform: translateY(-10px);
  box-shadow: 0 0 40px rgba(0, 0, 0, 0.4);
}

.products .card .product-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1 1 300px;
}

.products .card .product-info h3 {
  font-size: 1.75rem;
  color: #fc5d35;
}

.products .card .product-info p {
  color: #555;
  font-size: 1rem;
  line-height: 1.6;
}

.products .card .product-info .btn {
  margin-top: 1rem;
}

.products .card .product-info .btn button {
  padding: 1rem 2rem;
  background-color: #fc5d35;
  color: #fff;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.products .card .product-info .btn button:hover {
  background-color: #e64a29;
  transform: scale(1.05);
}

.products .card .image {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1 1 300px;
}

.products .card .image img {
  width: 300px;
  height:250px;
  object-fit: contain;
}

/* Background Image */
.background-image {
  background-image: url('../assets/tasty.png'); 
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 70vh; 
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  position: relative;
  margin-bottom: 5rem;
}

.background-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.4); 
  z-index: 0;
  border-radius: inherit;
}

.background-image h2 {
  color: #fff;
  font-size: 6rem;
  font-weight: bold;
  z-index: 1;
  position: relative;
}

/* Testimonials Section */

.testimonials {
  padding: 4rem 2rem;
  text-align: center;
  border-radius: 1rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.testimonials h2 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.testimonials p {
  font-size: 1.125rem;
  color: #666;
  line-height: 1.8;
}

.testimonials img {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 50%;
  margin: 1rem 0;
  border: 4px solid #fc5d35;
}

.testimonials h3 {
  font-size: 2rem;
  color: #fc5d35;
  margin-top: 0.5rem;
}

/* Contact Us Section */
.contact-us {
  position: relative;
  display: block;
  padding: 4rem 0;
}

/* Contact Form */
.contact-info {
  max-width: 600px;
  padding: 2rem;
  margin-left: 2rem;
  z-index: 2;
  position: relative;
}

.contact-info h2 {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 2rem;
}

.contact-info form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-info .form-group input,
.contact-info .form-group textarea {
  width: 100%;
  padding: 0.5rem;
  font-size: 1rem;
  border: none;
  border-bottom: 2px solid #ccc;
  background-color: transparent;
  outline: none;
  transition: border-color 0.3s ease;
}

.contact-info .form-group input:focus,
.contact-info .form-group textarea:focus {
  border-color: #fc5d35;
}

.contact-info .form-group textarea {
  min-height: 120px;
}

.contact-info .form-group button {
  width: fit-content;
  padding: 1rem 2rem;
  background-color: #fc5d35;
  color: #fff;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.contact-info .form-group button:hover {
  background-color: #e64a29;
  transform: scale(1.05);
}

/* Contact Image – Positioned to Right Side */
.contact-us-image {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.contact-us-image img {
  height: 100%;
  object-fit: cover;
  border-top-left-radius: 2rem;
  border-bottom-left-radius: 2rem;
}
/* Footer Section */
.footer {

  color: #333;
  padding-top: 4rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-column {
  flex: 1 1 220px;
  min-width: 200px;
}

.footer-column h3 {
  font-size: 1.3rem;
  color: #222;
  margin-bottom: 1rem;
  position: relative;
}

.footer-column h3::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -0.3rem;
  width: 40px;
  height: 3px;
  background-color: #fc5d35;
  border-radius: 3px;
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-column ul li {
  margin-bottom: 0.6rem;
}

.footer-column ul li a {
  color: #666;
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.footer-column ul li a:hover {
  color: #fc5d35;
  padding-left: 4px;
}

.footer-column.social h4 {
  font-size: 1rem;
  margin: 1.2rem 0 0.6rem;
  color: #222;
}

.social-icons {
  display: flex;
  gap: 0.8rem;
  align-items: center;
  margin-top: 0.5rem;
}

.social-icons a img {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.social-icons a:hover img {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.subscribe-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.subscribe-form input {
  padding: 0.6rem 0.75rem;
  border: 1px solid #ccc;
  border-radius: 0.4rem;
  font-size: 0.95rem;
  width: 100%;
}
.subscribe-form input:focus {
  outline: none;
  border-color: #fc5d35;
}

.subscribe-form button {
  background-color: #fc5d35;
  color: #fff;
  border: none;
  padding: 0.6rem;
  font-weight: bold;
  cursor: pointer;
  border-radius: 0.4rem;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.subscribe-form button:hover {
  background-color: #e64a29;
  transform: scale(1.03);
}

.footer-bottom {
  text-align: center;
  margin-top: 3rem;
  padding-top: 1rem;
  font-size: 1rem;
  font-weight: bold;
  color: #fff;
  background-color: #777;
  width: 100%;
  padding: 1rem 0;
}
